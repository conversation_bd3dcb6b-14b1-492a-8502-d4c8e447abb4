<div>
    <x-layout.row>
        <div class="col">
            <x-card.body>
                <!-- Step Progress Indicator -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="d-flex justify-content-center" style="gap: 4rem;">
                            @foreach($steps as $stepNumber => $stepLabel)
                                <div class="text-center">
                                    @if($editMode)
                                        <button
                                            type="button"
                                            wire:click="goToStep({{ $stepNumber }})"
                                            class="btn p-0 border-0"
                                            style="background: none;">
                                            <div class="d-flex flex-column align-items-center">
                                                <div class="rounded-circle d-flex align-items-center justify-content-center"
                                                     style="width: 50px; height: 50px; background-color: {{ $step === $stepNumber ? '#e9c5ff' : '#d1d5db' }}; color: {{ $step === $stepNumber ? '#7c3aed' : '#6b7280' }}; font-weight: 600; font-size: 1.1rem;">
                                                    {{ $stepNumber }}
                                                </div>
                                                <div class="mt-2" style="font-size: 0.875rem; color: {{ $step === $stepNumber ? '#7c3aed' : '#6b7280' }}; white-space: nowrap;">
                                                    {{ $stepLabel }}
                                                </div>
                                            </div>
                                        </button>
                                    @else
                                        <div class="d-flex flex-column align-items-center">
                                            <div class="rounded-circle d-flex align-items-center justify-content-center"
                                                 style="width: 50px; height: 50px; background-color: {{ $step === $stepNumber ? '#e9c5ff' : '#d1d5db' }}; color: {{ $step === $stepNumber ? '#7c3aed' : '#6b7280' }}; font-weight: 600; font-size: 1.1rem;">
                                                {{ $stepNumber }}
                                            </div>
                                            <div class="mt-2" style="font-size: 0.875rem; color: {{ $step === $stepNumber ? '#7c3aed' : '#6b7280' }}; white-space: nowrap;">
                                                {{ $stepLabel }}
                                            </div>
                                        </div>
                                    @endif
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>

                <!-- Step Content -->
                @if($step === 1)
                    @include('livewire.provider.provider-steps.step1')
                @elseif($step === 2)
                    @include('livewire.provider.provider-steps.step2')
                @elseif($step === 3)
                    @include('livewire.provider.provider-steps.step3')
                @elseif($step === 4)
                    @include('livewire.provider.provider-steps.step4')
                @endif
            </x-card.body>

            {{-- <x-group.errors /> --}}

            <x-card.footer>
                <div class="d-flex justify-content-between">
                    @if($step > 1)
                        <button type="button" class="btn btn-secondary" wire:click="prevStep" wire:loading.attr="disabled">
                            <i class="fas fa-arrow-left me-1"></i> Previous
                        </button>
                    @else
                        <div></div>
                    @endif

                    @if($step < $totalSteps)
                        <button type="button" class="btn btn-primary" wire:click="nextStep" wire:loading.attr="disabled">
                            Next <i class="fas fa-arrow-right ms-1"></i>
                        </button>
                    @else
                        <button type="button" class="btn btn-success" wire:click="submit" wire:loading.attr="disabled" wire:loading.class="disabled">
                            <span wire:loading.remove wire:target="submit">
                                <i class="fas fa-save me-1"></i> {{ $editMode ? 'Update Provider' : 'Create Provider' }}
                            </span>
                            <span wire:loading wire:target="submit">
                                <i class="fas fa-spinner fa-spin me-1"></i> {{ $editMode ? 'Updating...' : 'Creating...' }}
                            </span>
                        </button>
                    @endif
                </div>
            </x-card.footer>
        </div>
    </x-layout.row>
</div>
